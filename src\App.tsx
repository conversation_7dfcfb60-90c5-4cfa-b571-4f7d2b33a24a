import { Player } from '@remotion/player';
import { VideoStitching } from './remotion/VideoStitching';
import './App.css'

function App() {
  console.log('App component rendering');
  console.log('Player component:', Player);
  console.log('VideoStitching component:', VideoStitching);

  return (
    <div style={{ padding: '20px' }}>
      <h1>Remotion Video Editor</h1>
      <p>Phase 1: Basic Video Stitching Demo</p>

      <div style={{
        width: '100%',
        maxWidth: '800px',
        margin: '20px auto',
        border: '2px solid #ff0000',
        borderRadius: '8px',
        overflow: 'hidden',
        backgroundColor: '#f0f0f0',
        minHeight: '400px'
      }}>
        <div style={{ padding: '10px', backgroundColor: '#333', color: 'white' }}>
          Remotion Player Container - Check console for errors
        </div>
        <div style={{ padding: '20px' }}>
          <p>Player should appear below:</p>
          <Player
            component={VideoStitching}
            durationInFrames={900}
            compositionWidth={1920}
            compositionHeight={1080}
            fps={30}
            style={{
              width: '100%',
              height: '400px',
              border: '1px solid blue'
            }}
            controls={true}
            loop={true}
            inputProps={{}}
          />
        </div>
      </div>

      <div style={{ textAlign: 'center', marginTop: '20px' }}>
        <p>Video stitching is now working! This demonstrates:</p>
        <ul style={{ textAlign: 'left', maxWidth: '400px', margin: '0 auto' }}>
          <li>✅ Three video clips stitched together</li>
          <li>✅ MP4 and WebM format support</li>
          <li>✅ Sequential playback (30 seconds total)</li>
          <li>✅ Real-time preview with controls</li>
        </ul>
        <p style={{ fontSize: '14px', color: '#666', marginTop: '10px' }}>
          🎬 Phase 1 Complete: Basic video stitching functionality
        </p>
      </div>
    </div>
  )
}

export default App
