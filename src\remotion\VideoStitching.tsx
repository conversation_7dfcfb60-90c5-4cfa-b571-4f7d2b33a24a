import { AbsoluteFill, Sequence, Video, useCurrentFrame } from 'remotion';

// Video clips configuration
const videoClips = [
  {
    src: '/videos/clip1.mp4',
    durationInFrames: 300, // 10 seconds at 30fps
    name: 'Clip 1'
  },
  {
    src: '/videos/clip2.mp4',
    durationInFrames: 300, // 10 seconds at 30fps
    name: 'Clip 2'
  },
  {
    src: '/videos/clip3.webm',
    durationInFrames: 300, // 10 seconds at 30fps
    name: 'Clip 3'
  },
];

export const VideoStitching: React.FC = () => {
  const frame = useCurrentFrame();
  let currentStartFrame = 0;

  return (
    <AbsoluteFill style={{ backgroundColor: 'black' }}>
      {/* Video sequences */}
      {videoClips.map((clip, index) => {
        const sequence = (
          <Sequence
            key={index}
            from={currentStartFrame}
            durationInFrames={clip.durationInFrames}
          >
            <AbsoluteFill>
              <Video
                src={clip.src}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                }}
                volume={0.5}
              />
              {/* Overlay showing current clip info */}
              <div style={{
                position: 'absolute',
                top: 20,
                left: 20,
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                color: 'white',
                padding: '10px 15px',
                borderRadius: '5px',
                fontSize: 16,
                fontFamily: 'Arial, sans-serif'
              }}>
                {clip.name} - Frame: {frame}
              </div>
            </AbsoluteFill>
          </Sequence>
        );

        currentStartFrame += clip.durationInFrames;
        return sequence;
      })}
    </AbsoluteFill>
  );
};
